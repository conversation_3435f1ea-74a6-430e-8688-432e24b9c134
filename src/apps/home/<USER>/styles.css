.title {
    color: #000;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.sub-title {
    color: #716E6E;
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
}

/* Quick Access Cards Styles */
.quick-access-container {
    width: 100%;
}

.quick-access-card {
    border: 1px solid #e4e6ef;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    min-height: 120px;
    background: #ffffff;
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
}

.quick-access-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 2rem 0.5rem rgba(0, 0, 0, 0.15);
    border-color: #009ef7;
}

.quick-access-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f3ff;
    border-radius: 0.5rem;
}

.quick-access-icon-img {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
}

.quick-access-title {
    color: #181c32;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
}

.cursor-pointer {
    cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .quick-access-card {
        min-height: 100px;
    }

    .quick-access-icon {
        width: 35px;
        height: 35px;
    }

    .quick-access-icon-img {
        width: 20px;
        height: 20px;
    }

    .quick-access-title {
        font-size: 12px;
    }
}

@media (min-width: 768px) {
    .quick-access-card {
        min-height: 130px;
    }
}

@media (min-width: 992px) {
    .quick-access-card {
        min-height: 140px;
    }
}