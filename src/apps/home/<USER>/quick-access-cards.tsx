/** @jsxImportSource solid-js */
import { Card, Col, Row } from 'solid-bootstrap';
import { useScreenWidth } from '../../../shared/hooks/use-screen-width';
import heartIcon from '../../../assets/media/icons/Heart.svg';
import goalIcon from '../../../assets/media/icons/Target, Success.svg';
import labIcon from '../../../assets/media/icons/capsule in bottle.svg';
import '../styles/styles.css';

interface QuickAccessItem {
  title: string;
  icon: string;
  href: string;
}

const QuickAccessCards = () => {
  const screenWidth = useScreenWidth();

  const quickAccessItems: QuickAccessItem[] = [
    {
      title: 'Hábitos',
      icon: heartIcon,
      href: '../health/?view=HEALTH',
    },
    {
      title: 'Laboratorio',
      icon: labIcon,
      href: '../health/?view=LAB_EXAMS',
    },
    {
      title: 'Expediente Médico',
      icon: heartIcon,
      href: '../medical-record/',
    },
    {
      title: 'Retos Activos',
      icon: goalIcon,
      href: '../challenges/?view=HOME',
    },
  ];

  const handleCardClick = (href: string) => {
    window.location.href = href;
  };

  return (
    <div class="quick-access-container mb-5">
      <Row class="g-3">
        {quickAccessItems.map((item) => (
          <Col
            xs={6}
            sm={6}
            md={3}
            lg={3}
            class="quick-access-col"
          >
            <Card
              class="quick-access-card h-100 cursor-pointer"
              onClick={() => handleCardClick(item.href)}
            >
              <Card.Body class="d-flex flex-column align-items-center justify-content-center text-center p-3">
                <div class="quick-access-icon mb-3">
                  <img
                    src={item.icon}
                    alt={item.title}
                    class="quick-access-icon-img"
                  />
                </div>
                <h6 class="quick-access-title mb-0">{item.title}</h6>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default QuickAccessCards;
